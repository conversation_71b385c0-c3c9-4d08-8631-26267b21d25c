import { GridsterItem } from 'angular-gridster2';
import { Data, Layout } from 'plotly.js';

// UI RELATED TYPES
export interface PlotDataUI {
  id: number;
  name: string;
  plot_type_name: string;
  file_name: string;
  fileId: number;
  filter_active: boolean;
  filter_instance_id?: number | null;
  json_data: { data: Data[]; layout: Layout };
  display_layout: GridsterItem | null;
  favorite?: boolean;
}

export interface PlotFilterDataUI {
  fileId: number;
  plotId: number;
  filterInstanceId: number | null;
  isFilterActive: boolean;
}

export interface GridsterItemWithPlotly extends Partial<GridsterItem> {
  plot_id: number;
}

export interface FilterDataRow {
  logicOperator: string;
  selectedColumn: string;
  selectedOperator: string;
  isBetweenOperator: boolean;
  columnType?: string;
  availableOperators?: string[];
  availableValues?: string[];
  selectedValue: string | null;
  minValue?: string | null;
  maxValue?: string | null;
  singleValue?: string | null;
  minPlaceholder?: string;
  maxPlaceholder?: string;
  showValues?: boolean;
}

// RESPONSE RELATED TYPES
export interface LayoutResFormatFromSaveDisplayLayoutAPI {
  plot_id: number;
  display_layout: GridsterItem;
}

export interface SetFavoritePlotResponse {
  plot_id: number;
  favorite: boolean;
}
