<div
  *ngIf="loading"
  class="absolute inset-0 flex justify-center items-center bg-gray-500 bg-opacity-50 backdrop-blur-sm z-10">
  <app-loader [loading]="loading"></app-loader>
</div>
<div class="p-6">
  <div class="flex justify-between">
    <div class="text-s">
      You can run a model on new data either by entering it ma on table data or
      image data
    </div>
    <button
      mat-icon-button
      (click)="OnCloseModal()"
      class="text-setSettinggray-400 hover:text-gray-600">
      <mat-icon>close</mat-icon>
    </button>
  </div>
  <mat-dialog-content
    class="grid grid-rows-[auto_auto_auto] gap-4 overflow-hidden">
    <!-- Section 1: Tab Group -->
    <div>
      <mat-tab-group animationDuration="0ms" class="custom-tab-group">
        <mat-tab label="Enter Manually">
          <div class="mt-4">
            <!-- Headers -->
            <div class="grid grid-cols-2 mb-2 gap-6">
              <h4 class="text-sm font-medium text-gray-700">Columns</h4>
              <h4 class="text-sm font-medium text-gray-700">Values</h4>
            </div>
            <!-- Enter manually Rows -->
            <div>
              <!-- Feature Rows -->
              <form
                [formGroup]="predictForm"
                scrollTracker
                class="overflow-y-visible">
                <div
                  class="space-y-4 h-[239px] overflow-auto"
                  formArrayName="entries">
                  @for (
                    entry of entries.controls;
                    track entry;
                    let i = $index
                  ) {
                    <div [formGroupName]="i" class="grid grid-cols-2 gap-6">
                      <div
                        class="bg-[#6750A414] p-3 border border-gray-300 rounded">
                        {{ entry.get('name')?.value }}
                      </div>
                      <ng-container>
                        @switch (entry.get('type')?.value) {
                          @case ('FloatField') {
                            <input
                              type="number"
                              step="any"
                              placeholder="FloatField"
                              formControlName="value"
                              class="w-full p-3 border border-gray-300 rounded" />
                          }
                          @case ('IntegerField') {
                            <input
                              type="number"
                              step="1"
                              placeholder="IntegerField"
                              formControlName="value"
                              class="w-full p-3 border border-gray-300 rounded"
                              (keydown)="preventDecimal($event)" />
                          }
                          @default {
                            <mat-select
                              class="w-full bg-white p-3 border border-gray-300 outline-none border py-2 shadow-sm h-12 rounded"
                              placeholder="CharField"
                              formControlName="value">
                              @for (
                                option of getCategoricalOptions(
                                  entry.get('options')?.value
                                );
                                track option
                              ) {
                                <mat-option [value]="option">
                                  {{ option }}
                                </mat-option>
                              }
                            </mat-select>
                          }
                        }
                      </ng-container>
                    </div>
                  }
                </div>
              </form>
              <!-- Section 2: Divider -->
              <div>
                <hr class="my-4" />
              </div>
              <!-- Section 3: Final Output Row -->
              <div class="mt-auto">
                @if (result) {
                  <div class="grid grid-cols-2 gap-6">
                    <div
                      class="w-full bg-[#6750A414] p-3 border border-gray-300 rounded">
                      {{ result.predicted_column }}
                    </div>
                    <div
                      class="w-full bg-[#6750A414] p-3 border border-gray-300 rounded">
                      {{ result.predicted_values }}
                    </div>
                  </div>
                }

                <mat-dialog-actions class="flex justify-center p-0">
                  <button
                    mat-flat-button
                    color="primary"
                    [disabled]="predictForm.invalid"
                    (click)="getPredictions()">
                    @if (predictLoader) {
                      <mat-icon class="h-[20px] w-[25px]"
                        ><mat-spinner color="accent" diameter="20">
                        </mat-spinner
                      ></mat-icon>
                    }
                    Predict
                  </button>
                </mat-dialog-actions>
              </div>
            </div>
          </div>
        </mat-tab>
        <!-- Upload File Section -->
        <!-- <mat-tab label="Upload File">
          <div class="upload-file-container mt-[50px]">
            <ngx-file-drop dropZoneLabel="Drop files or folders here">
              <ng-template
                ngx-file-drop-content-tmp
                let-openFileSelector="openFileSelector">
                <div class="w-[366px] cursor-pointer">
                  <p
                    class="text-[#73777F]"
                    (click)="openFileSelector()"
                    (keyup.enter)="openFileSelector()"
                    tabindex="0">
                    Drag and Drop your files and folders here, or
                    <a> Browse File.</a>
                  </p>
                  <p class="text-[#73777F]" style="text-align: center">
                    (csv, parquet, jpg or png)
                  </p>
                </div>
              </ng-template>
            </ngx-file-drop>
          </div>
          <mat-dialog-actions class="flex">
            <button mat-flat-button (click)="getPredictions()">
              Download<mat-icon>download</mat-icon>
            </button>
          </mat-dialog-actions>
        </mat-tab> -->
      </mat-tab-group>
    </div>
  </mat-dialog-content>
</div>
