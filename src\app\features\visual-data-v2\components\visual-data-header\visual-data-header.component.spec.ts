import { ComponentFixture, TestBed } from '@angular/core/testing';

import { VisualDataHeaderComponent } from './visual-data-header.component';

describe('VisualDataHeaderComponent', () => {
  let component: VisualDataHeaderComponent;
  let fixture: ComponentFixture<VisualDataHeaderComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [VisualDataHeaderComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(VisualDataHeaderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
