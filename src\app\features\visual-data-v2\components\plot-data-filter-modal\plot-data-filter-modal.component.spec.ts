import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PlotDataFilterModalComponent } from './plot-data-filter-modal.component';

describe('PlotDataFilterModalComponent', () => {
  let component: PlotDataFilterModalComponent;
  let fixture: ComponentFixture<PlotDataFilterModalComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PlotDataFilterModalComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(PlotDataFilterModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
