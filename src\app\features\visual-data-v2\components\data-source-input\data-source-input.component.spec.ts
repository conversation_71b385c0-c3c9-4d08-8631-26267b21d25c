import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DataSourceInputComponent } from './data-source-input.component';

describe('DataSourceInputComponent', () => {
  let component: DataSourceInputComponent;
  let fixture: ComponentFixture<DataSourceInputComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DataSourceInputComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(DataSourceInputComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
