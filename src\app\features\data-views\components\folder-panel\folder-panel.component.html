<mat-expansion-panel
  [expanded]="expandedPanels[folder.id]"
  (opened)="getSubfolderData(folder.id || folder.folder_id)"
  (closed)="togglePanel(folder.id || folder.folder_id)">
  <mat-expansion-panel-header
    class="border-line rounded-none"
    [ngClass]="{ highlight_row: active }"
    style="display: flex; align-items: center">
    <mat-icon>folder_open</mat-icon>
    <span class="ml-2 metadata">{{
      folder.folder_name || folder.name || 'Unnamed Folder'
    }}</span>
    <div class="flex items-center hidden-on-hover" style="margin-left: auto">
      <mat-icon
        *ngIf="folder.id !== rootFolderId"
        (click)="confirmDeleteFolder(folder.id)"
        style="margin-right: 8px"
        >delete_outline</mat-icon
      >
    </div>
  </mat-expansion-panel-header>

  <div class="ml-6">
    <div
      *ngFor="let file of folder.files"
      class="file-item border-line rounded-none flex items-center justify-between">
      <div class="table-file flex items-center">
        <!-- Conditionally show the image if the file ends with .png, else show the mat-icon -->
        <ng-container
          *ngIf="
            file.name?.endsWith('.png') || file.file_name?.endsWith('.png');
            else tableIcon
          ">
          <mat-icon class="mr-2">crop_original</mat-icon>
        </ng-container>
        <ng-template #tableIcon>
          <mat-icon fontSet="material-icons-outlined" class="mr-2"
            >table_chart</mat-icon
          >
        </ng-template>

        <button class="py-[10px]" (click)="handleShowTable(file.id)">
          {{ file.name || file.file_name }}
        </button>
      </div>
      <div class="flex items-center hidden-on-hover">
        <button class="view-file-btn mr-4" (click)="handleViewFile(file.id)">
          View File
        </button>
        <mat-icon class="delete-icon" (click)="confirmDeleteFile(file.id)"
          >delete_outline</mat-icon
        >
      </div>
    </div>
    <div class="px-2 py-[5px]">
      <button
        *ngIf="showLoadMore"
        class="cursor-pointer"
        (click)="loadMoreData(folder.folder_id || folder.id)">
        Load more..
      </button>
    </div>

    <ng-container *ngIf="folder.subfolders?.length">
      <ng-container *ngFor="let subfolder of folder.subfolders">
        <app-folder-panel
          [folder]="subfolder"
          [rootFolderId]="rootFolderId"
          [showTable]="showTable"
          [viewFile]="viewFile"
          [deleteFile]="deleteFile"
          [deleteFolder]="deleteFolder"
          [PreviewFiles]="PreviewFiles"></app-folder-panel>
      </ng-container>
    </ng-container>
  </div>
</mat-expansion-panel>

<!-- Add the delete modal component here -->
<app-delete-modal
  *ngIf="isFileDeletePopup"
  (deleteEvent)="DeleteFileData(fileIDToDelete!)"
  (cancelEvent)="closePopup()">
</app-delete-modal>

<app-delete-modal
  *ngIf="isFolderDeletePopup"
  (deleteEvent)="DeleteFolderData(folderIDToDelete!)"
  (cancelEvent)="closePopup()">
</app-delete-modal>
