import { Component, inject, OnInit } from '@angular/core';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { FormArray, FormControl, FormGroup, FormBuilder } from '@angular/forms';
import { DataSourceInputComponent } from '../data-source-input/data-source-input.component';

type OptionSet = FormGroup<{
  optionName: FormControl<string>;
  optionDescription: FormControl<string>;
  required: FormControl<boolean>;
  multiple: FormControl<boolean>;
  aggregation: AggregationSet;
  datatype: FormArray<FormControl<string>>;
}>;

type AggregationSet = FormGroup<{
  aggregationType: FormControl<string>;
  aggregationDescription: FormControl<string>;
}>;

type SettingsSet = FormGroup<{
  settingName: FormControl<string>;
  settingDescription: FormControl<string>;
  multiple: FormControl<boolean>;
  settingsType: FormControl<string>;
}>;
type PlotCreationForm = FormGroup<{
  plotType: FormControl<string>;
  selectedFileInfo: FormGroup<{
    fileName: FormControl<string>;
    fileId: FormControl<string>;
  }>;
  selectedOptions: FormArray<OptionSet>;
  selectedSettings: FormArray<SettingsSet>;
}>;

@Component({
  standalone: true,
  selector: 'app-plot-creation-modal',
  imports: [MatDialogModule, MatButtonModule, DataSourceInputComponent],
  templateUrl: './plot-creation-modal.component.html',
  styleUrls: ['./plot-creation-modal.component.css'],
})
export class PlotCreationModalComponent implements OnInit {
  popupRef = inject(MatDialogRef<PlotCreationModalComponent>);
  fb = inject(FormBuilder);
  form!: PlotCreationForm;

  ngOnInit(): void {
    this.form = new FormGroup({
      plotType: this.fb.control('', { nonNullable: true }),
      selectedFileInfo: this.fb.group({
        fileName: this.fb.control('', { nonNullable: true }),
        fileId: this.fb.control('', { nonNullable: true }),
      }),
      selectedOptions: this.fb.array<OptionSet>([]),
      selectedSettings: this.fb.array<SettingsSet>([]),
    }) as PlotCreationForm;
  }

  createOptionSet(): OptionSet {
    return this.fb.group({
      optionName: this.fb.control('', { nonNullable: true }),
      optionDescription: this.fb.control('', { nonNullable: true }),
      required: this.fb.control(false, { nonNullable: true }),
      multiple: this.fb.control(false, { nonNullable: true }),
      aggregation: this.fb.group({
        aggregationType: this.fb.control('', { nonNullable: true }),
        aggregationDescription: this.fb.control('', { nonNullable: true }),
      }) as AggregationSet,
      datatype: this.fb.array<FormControl<string>>([]),
    }) as OptionSet;
  }

  createSettingsSet(): SettingsSet {
    return this.fb.group({
      settingName: this.fb.control('', { nonNullable: true }),
      settingDescription: this.fb.control('', { nonNullable: true }),
      multiple: this.fb.control(false, { nonNullable: true }),
      settingsType: this.fb.control('', { nonNullable: true }),
    });
  }

  closePlotCreationModal() {
    this.popupRef.close();
  }
}
