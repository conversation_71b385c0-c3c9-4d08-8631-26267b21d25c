<div class="flex flex-row justify-between align-middle">
  <div>
    <div>{{ this.visualDataStore.projectData()?.title }}</div>
    <div class="text-3xl">Visual Data Insights</div>
  </div>
  <div class="flex flex-row gap-4">
    <div class="flex items-center justify-center">
      <app-search-header
        (searchPerformed)="searchFilters($event)"></app-search-header>
    </div>
    <div class="flex items-center">
      <button
        mat-icon-button
        class="mr-1 ml-1 text-black"
        (click)="toggleShowFavouritePlots()"
        [disabled]="this.visualDataStore.isLoading()">
        @if (this.visualDataStore.showFavouritePlots() === true) {
          <mat-icon>star</mat-icon>
        } @else {
          <mat-icon>star_border</mat-icon>
        }
        <mat-icon>star_border</mat-icon>
      </button>
    </div>
    <div class="flex items-center">
      <button
        mat-flat-button
        (click)="this.openPlotCreationModal()"
        class="whitespace-nowrap flex rounded-lg items-center h-12">
        <mat-icon>add</mat-icon>
        <span> {{ g_const.newPlot }}</span>
      </button>
    </div>
  </div>
</div>
