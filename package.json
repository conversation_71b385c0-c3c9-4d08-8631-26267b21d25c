{"name": "aicu", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prettier": "npx prettier --write .", "prepare": "husky", "lint": "ng lint", "serve:ssr:AICU": "node dist/aicu/server/server.mjs", "schema": "npx openapi-typescript ./src/app/schema/schema.yaml -o ./src/app/schema/schema.ts"}, "private": true, "dependencies": {"@angular-architects/ngrx-toolkit": "^19.1.0", "@angular-eslint/eslint-plugin": "^19.0.2", "@angular-eslint/eslint-plugin-template": "^19.0.2", "@angular-eslint/template-parser": "^19.0.2", "@angular/animations": "^19.1.3", "@angular/cdk": "^19.1.1", "@angular/common": "^19.1.3", "@angular/compiler": "^19.1.3", "@angular/core": "^19.1.3", "@angular/forms": "^19.1.3", "@angular/material": "^19.1.1", "@angular/platform-browser": "^19.1.3", "@angular/platform-browser-dynamic": "^19.1.3", "@angular/platform-server": "^19.1.3", "@angular/router": "^19.1.3", "@angular/ssr": "^19.1.4", "@iplab/ngx-color-picker": "^19.0.0", "@ng-select/ng-select": "^14.2.0", "@ngrx/effects": "^19.0.1", "@ngrx/signals": "^19.1.0", "@ngrx/store": "^19.0.1", "@ngrx/store-devtools": "^19.0.1", "@stripe/stripe-js": "^5.5.0", "@typescript-eslint/eslint-plugin": "^8.21.0", "angular-gridster2": "^19.0.0", "angular-plotly.js": "^6.0.0", "canvas-confetti": "^1.9.3", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "express": "^4.21.2", "helmet": "^8.0.0", "husky": "^9.1.7", "intro.js": "^7.2.0", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "ngx-file-drop": "^16.0.0", "ngx-stripe": "^19.0.0", "ngx-toastr": "^19.0.0", "plotly.js": "^2.35.3", "plotly.js-dist": "^2.35.3", "plotly.js-dist-min": "^2.35.3", "posthog-js": "^1.210.2", "prettier": "^3.4.2", "prettier-eslint": "^16.3.0", "rxjs": "~7.8.1", "tslib": "^2.8.1", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.4", "@angular/cli": "^19.1.4", "@angular/compiler-cli": "^19.1.3", "@tailwindcss/postcss": "^4.0.0", "@types/canvas-confetti": "^1.9.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.10", "@types/plotly.js": "^2.35.2", "@types/plotly.js-dist-min": "^2.3.4", "angular-eslint": "^19.0.2", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-unused-imports": "^4.1.4", "jest": "^29.7.0", "jest-preset-angular": "^14.5.1", "openapi-typescript": "^7.6.0", "postcss": "^8.5.1", "tailwindcss": "^3.0.0", "ts-jest": "^29.2.5", "typescript": "^5.7.3", "typescript-eslint": "8.21.0"}}