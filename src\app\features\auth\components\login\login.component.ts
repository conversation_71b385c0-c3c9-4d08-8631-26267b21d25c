import { Component, OnInit } from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { Router } from '@angular/router';
import { MatSidenavModule } from '@angular/material/sidenav';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { g_const } from '../../../../_utility/global_const';
import { AuthService } from '../../services/auth.service';
import { MatIcon } from '@angular/material/icon';
import { emailValidator } from '../../services/validators';
import { Store } from '@ngrx/store';
import { changeTab } from '../../../../core/store/actions/tab.action';

@Component({
  selector: 'app-login',
  imports: [
    MatFormFieldModule,
    MatSidenavModule,
    MatInputModule,
    MatCheckboxModule,
    MatButtonModule,
    CommonModule,
    MatIcon,
    ReactiveFormsModule,
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css',
})
export class LoginComponent implements OnInit {
  g_const = g_const;
  loginForm!: FormGroup;
  errorMessage = '';

  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private toastrService: ToastrService,
    private store: Store,
  ) {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email, emailValidator]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      keep_me_logged_in: [false],
    });
  }

  ngOnInit(): void {
    this.loginForm.get('keep_me_logged_in')?.valueChanges.subscribe(value => {
      console.log('Keep me logged in changed:', value);
    });
  }

  navigateTosignUp() {
    this.router.navigate(['/auth/register']);
  }

  navigateToForgetPassword() {
    this.router.navigate(['auth/forget-password']);
  }

  onSubmit(): void {
    this.errorMessage = '';
    if (this.loginForm.valid) {
      const formData = this.loginForm.value;
      this.authService.login(formData).subscribe({
        next: res => {
          localStorage.setItem('access_token', res.accesstoken);
          localStorage.setItem('email', formData.email);
          this.store.dispatch(changeTab({ selectedTab: 'projects' }));

          if (formData.keep_me_logged_in) {
            localStorage.setItem('refreshtoken', res.refreshtoken);
          } else {
            sessionStorage.setItem('refreshtoken', res.refreshtoken);
          }

          this.router.navigateByUrl('/dashboard/projects');
        },
        error: err => {
          this.toastrService.error(`${err.error.error}`);
        },
      });
    } else {
      this.loginForm.markAllAsTouched();
    }
  }
  passwordFieldType = 'password';

  togglePasswordVisibility(): void {
    this.passwordFieldType =
      this.passwordFieldType === 'password' ? 'text' : 'password';
  }
}
