import { Component } from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

import { ToastrService } from 'ngx-toastr';
import { g_const } from '../../../../_utility/global_const';
import { AuthService } from '../../services/auth.service';
import { emailValidator } from '../../services/validators';

@Component({
  selector: 'app-forget-password',
  imports: [
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    CommonModule,
    ReactiveFormsModule,
  ],
  templateUrl: './forget-password.component.html',
  styleUrl: './forget-password.component.css',
})
export class ForgetPasswordComponent {
  g_const = g_const;
  forgotPassword: FormGroup;
  errorMessage = '';

  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private toastrService: ToastrService,
  ) {
    this.forgotPassword = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email, emailValidator]],
    });
  }

  navigateTosignUp() {
    this.router.navigateByUrl('/auth/register');
  }

  onSubmit(): void {
    this.authService.forgetPassword(this.forgotPassword.value).subscribe({
      next: res => {
        this.toastrService.success(`${res.message}`);
        this.router.navigate(['/auth/new-password']);
      },
      error: err => {
        this.toastrService.error(`${err.error.error}`);
      },
    });
  }
}
