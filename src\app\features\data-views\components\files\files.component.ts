import { Component, OnInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute } from '@angular/router';
import { PageEvent } from '@angular/material/paginator';
import { DataviewService } from '../../services/data-view.service';
import { NgForm } from '@angular/forms';
import { forkJoin } from 'rxjs';
import {
  ColumnValues,
  DynamicAddColumn,
  FileData,
  FilterDataRow,
  FilterOperators,
} from '../../models/data-view.model';
import { BackendResponse } from '../../../../_models/visual-data/visual-data.model';
import { MatSelectChange } from '@angular/material/select';

@Component({
  selector: 'app-files',
  templateUrl: './files.component.html',
  styleUrls: ['./files.component.css'],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class FilesComponent implements OnInit {
  imageDataOutput: FileData | null = null;
  isTableFile: boolean | null = null;
  filename = '';
  filter_active = false;
  filter_obj_id: number | null = null;
  displayedColumns: string[] = [];
  dataSource = new MatTableDataSource<any>([]); // eslint-disable-line @typescript-eslint/no-explicit-any
  isModalOpen = false;
  selectedColumn = '';
  loading = false;
  fileID: number | null = null;
  pageSize = 10;
  totalPages = 1;
  nextPage: string | null = null;
  previousPage: string | null = null;
  currentPageIndex = 0;
  isDropdownOpen = false;
  activeDropdown: string | null = null;
  column = '';
  statisticsData: Record<string, any> = {}; // eslint-disable-line @typescript-eslint/no-explicit-any
  // // Set to any for flexibility
  statisticsFileType = 'numeric';
  processed = false;
  section = 'data';
  dataStatsisticsType = 'numericalColumns';
  isFilterDataModalOpen = false;
  isAddColumnModalOpen = false;
  categories: string[] = [];
  numerical: string[] = [];
  addColumn1Numerical: string[] = [];
  addColumn2Numerical: string[] = [];
  columnType = '';
  selectedOperator = '';
  selectedAddColumn = '';
  columnPosition = '';
  applicableIdColumns: string[] = [];
  availableOperators: FilterOperators | null = null;
  availableValues: ColumnValues | null = null;
  tooltipMessage = '';
  tooltipData: FilterOperators | null = null;
  newColumnName = '';
  selectedColumn1 = '';
  selectedAddColOperator = '';
  selectedColumn2 = '';
  showDropdown = false;
  // flag to check whether the add column form is submitted
  isAddColumnFormSubmitted = false;

  dynamicAddColumn: DynamicAddColumn[] = [
    { col1: '', operation: '', col2_or_scalar: '', metric_name: '' },
  ];

  filterDataRows: FilterDataRow[] = [
    {
      logicOperator: '',
      selectedColumn: '',
      selectedOperator: '',
      isBetweenOperator: false,
      columnType: '',
      availableOperators: [],
      availableValues: [],
      selectedValue: '',
      minValue: null,
      maxValue: null,
      singleValue: null,
      minPlaceholder: '',
      maxPlaceholder: '',
      showValues: false,
    },
  ];

  // the operators list options for the add column modal
  addColumnOperations: string[] = ['add', 'subtract', 'multiply', 'divide'];

  // operations icon mapping to show proper symbol beside the operations
  operationsIconMapping: Record<string, string> = {
    eq: '=',
    neq: '≠',
    contains: '',
    startswith: '',
    endswith: '',
    regex: '',
    gt: '>',
    gte: '≥',
    lt: '<',
    lte: '≤',
    between: '[ ]',
    add: '+',
    subtract: '-',
    multiply: 'x',
    divide: '÷',
  };

  constructor(
    private route: ActivatedRoute,
    private dataViewService: DataviewService,
    private toastrService: ToastrService,
  ) {}

  ngOnInit(): void {
    this.setPageSize();
    this.route.paramMap.subscribe(params => {
      this.loading = true;
      this.fileID = Number(params.get('fileID'));
      this.loadData()
      this.toastrService.success("Data retrieved successfully`);
    });
    this.getDataTypeInfo();
    window.addEventListener('resize', () => this.setPageSize());
  }

  setPageSize(): void {
    const height = window.innerHeight;

    if (height < 600) {
      this.pageSize = 5;
    } else if (height < 900) {
      this.pageSize = 8;
    } else if (height < 1180) {
      this.pageSize = 12;
    } else if (height < 1480) {
      this.pageSize = 16;
    } else {
      this.pageSize = 20;
    }
  }

  loadData(): void {
    this.dataViewService
      .ViewFileData(String(this.fileID), this.pageSize)
      .subscribe(
        (response: BackendResponse<FileData>) => {
          this.loading = false;
          console.log("response", response);
          if (response.status === 'success' && response.data.columns) {
            this.isTableFile = true;
            this.getColumnIds();
            // this.toastrService.success(`${response.message}`);
            this.totalPages = response.pagination?.count ?? 1;
            this.nextPage = response.pagination?.next ?? null;
            this.previousPage = response.pagination?.previous ?? null;
            this.displayedColumns = response.data.columns;
            this.dataSource.data = Object.values(response.data.data);
            this.filter_active = response.data.metadata.filter_active;
            this.filter_obj_id = response.data.metadata.filter_obj_id;
            this.filename = response.data.metadata.filename;
            this.loadExistingFilter();
          } else if (response.status === 'success' && response.data.json_data) {
            this.imageDataOutput = response.data;

            this.isTableFile = false;
          } else {
            this.toastrService.error(
              'Error retrieving data: ' + response.message,
            );
          }
        },
        error => {
          this.loading = false;
          this.toastrService.error(`${error.error.message}`);
          console.error('Error:', error);
        },
      );
  }
  getColumnIds(): void {
    this.dataViewService.getColumnID(this.fileID).subscribe(
      response => {
        if (response.status === 'success') {
          this.applicableIdColumns =
            response.data?.['Choices for ID_Columns'] || [];
        } else {
          this.toastrService.error(
            'Error retrieving data: ' + response.message,
          );
        }
      },
      error => {
        this.loading = false;
        this.toastrService.error(`${error.error.message}`);
        console.error('Error:', error);
      },
    );
  }

  loadExistingFilter() {
    if (this.filter_obj_id) {
      this.dataViewService.GetFilter(this.filter_obj_id).subscribe(
        response => {
          if (response.status === 'success') {
            const filterOperations = response.data.filter_operations;

            this.filterDataRows = filterOperations.map(
              (operation: {
                filter_operator: string;
                filter_value: string;
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                logic: any;
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                filter_column: any;
              }) => {
                const isBetweenOperator =
                  operation.filter_operator === 'between';
                let minValue = null;
                let maxValue = null;
                let selectedValue = null;

                // Handle 'between' operator case
                if (isBetweenOperator) {
                  const values = operation.filter_value
                    .split(',')
                    .map(val => val.trim());
                  minValue = values[0] || null;
                  maxValue = values[1] || null;
                } else {
                  selectedValue = operation.filter_value || null;
                }
                console.log(operation.filter_value);
                return {
                  logicOperator: operation.logic || '',
                  selectedColumn: operation.filter_column || '',
                  selectedOperator: operation.filter_operator || '',
                  isBetweenOperator: isBetweenOperator,
                  availableOperators: [operation.filter_operator],
                  minValue: minValue,
                  maxValue: maxValue,
                  singleValue: !isBetweenOperator ? selectedValue : null,
                  minPlaceholder: 'Min',
                  maxPlaceholder: 'Max',
                  selectedValue: selectedValue,
                  availableValues: [],
                  columnType: '',
                };
              },
            );
          }
        },
        error => {
          console.error('Error fetching column values:', error);
        },
      );
    }
  }

  onPageChange(event: PageEvent): void {
    this.loading = true;

    if (this.fileID) {
      this.dataViewService
        .ViewFileData(
          this.fileID.toString(),
          this.pageSize,
          event.pageIndex + 1,
        )
        .subscribe({
          next: response => {
            this.loading = false;
            if (response.status === 'success') {
              this.displayedColumns = response.data.columns;
              this.dataSource.data = Object.values(response.data.data);
            } else {
              this.toastrService.error(
                'Error retrieving data: ' + response.message,
              );
            }
          },
          error: error => {
            this.loading = false;
            console.error('Error fetching projects:', error);
          },
        });
    }

    this.currentPageIndex = event.pageIndex;
  }

  handleRandomFileDta(): void {
    this.dataViewService
      .randomFileData(
        this.fileID,
        this.currentPageIndex + 1,
        this.pageSize,
        true,
      )
      .subscribe(
        response => {
          this.toastrService.success(`${response.message}`);
          this.totalPages = response.pagination?.count ?? 1;
          this.nextPage = response.pagination?.next ?? null;
          this.previousPage = response.pagination?.previous ?? null;
          this.displayedColumns = response.data.columns;
          this.dataSource.data = Object.values(response.data.data);
        },
        error => {
          console.error(error);
        },
      );
  }

  handleTypeChange(section: string): void {
    this.dataStatsisticsType = section;
    this.statisticsFileType =
      this.dataStatsisticsType === 'numericalColumns'
        ? 'numeric'
        : 'non-numeric';

    if (this.section === 'statistics') {
      const type = this.statisticsFileType;
      this.dataViewService
        .getStatisticalValues(this.fileID, type, this.processed)
        .subscribe(
          response => {
            this.statisticsData = response.data;
          },
          error => {
            console.error(error);
          },
        );
    }
  }

  handleProcessedChange(section: string): void {
    this.processed = section === 'processed';

    if (this.section === 'statistics') {
      this.dataViewService
        .getStatisticalValues(
          this.fileID,
          this.statisticsFileType,
          this.processed,
        )
        .subscribe(
          response => {
            this.statisticsData = response.data;
          },
          error => {
            console.error(error);
          },
        );
    }
  }

  getTableHeaders(): string[] {
    const firstKey = Object.keys(this.statisticsData)[0];
    if (firstKey) {
      return Object.keys(this.statisticsData[firstKey]);
    }
    return [];
  }
  replaceUnderscoreWithSpace(input: string): string {
    return input.replace(/_/g, ' ');
  }
  capitalizeFirstLetter(value: string): string {
    if (!value) return '';
    return value.charAt(0).toUpperCase() + value.slice(1);
  }
  handleSectionChange(section: string): void {
    this.section = section;
    if (section === 'statistics') {
      this.dataViewService
        .getStatisticalValues(this.fileID, 'numeric')
        .subscribe(
          response => {
            this.statisticsData = response.data;
            this.totalPages = response.pagination?.total_pages ?? 1;
          },
          error => {
            console.error(error);
          },
        );
    } else if (section === 'data') {
      this.loadData();
    }
  }

  getDataTypeInfo() {
    if (this.fileID) {
      this.dataViewService.getColumnValues(this.fileID).subscribe(res => {
        this.tooltipData = res.data.operators;
        this.tooltipMessage = this.extractUniqueTooltips(res.data.operators);
        console.log(this.tooltipMessage);
      });
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
  extractUniqueTooltips(_data: any): string {
    const uniqueOperations = new Map<string, string>();
    for (const category in this.tooltipData) {
      const operations = this.tooltipData[category];
      for (const key in operations) {
        if (!uniqueOperations.has(key)) {
          uniqueOperations.set(key, operations[key]);
        }
      }
    }

    let tooltipText = '';
    uniqueOperations.forEach((description, key) => {
      tooltipText += `${key}: ${description}\n`;
    });

    return tooltipText.trim();
  }

  downloadData(): void {
    const downloadFile = (response: Blob, fileName: string): void => {
      const url = URL.createObjectURL(response);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      a.click();
      URL.revokeObjectURL(url);
    };

    if (this.section === 'data') {
      this.dataViewService.DownloadFileData(this.fileID).subscribe(
        (response: Blob) => {
          const fileName = `file_${this.fileID}.csv`;
          downloadFile(response, fileName);
        },
        error => {
          console.error('Download failed:', error);
        },
      );
    } else {
      console.log([this.statisticsFileType]);
      this.dataViewService
        .DownloadFileStatisticsData(
          this.fileID,
          this.statisticsFileType,
          this.processed,
        )
        .subscribe(
          (response: Blob) => {
            const fileName = `file_Statistics_${this.fileID}.csv`;
            downloadFile(response, fileName);
          },
          error => {
            console.error('Download failed:', error);
          },
        );
    }
  }

  toggleDropdown(column: string): void {
    this.activeDropdown = this.activeDropdown === column ? null : column;
  }

  closeModal(): void {
    this.isModalOpen = false;
    this.selectedColumn = '';
  }

  saveChanges(): void {
    this.toastrService.success('Changes saved successfully!');
    this.closeModal();
  }

  discardChanges(): void {
    this.toastrService.info('Changes discarded.');
    this.closeModal();
  }

  openModal(column: string): void {
    this.selectedColumn = column;
    this.isModalOpen = true;
  }

  selectAsId(column: string): void {
    this.dataViewService.updateColumnID(this.fileID, column).subscribe(
      response => {
        if (response.status === 'success') {
          console.log('column succesfully set');
        }
      },
      error => {
        console.error('Error fetching column values:', error);
      },
    );
  }

  filterData(column?: string): void {
    this.loadExistingFilter();
    this.openFilterDataModal(column);
  }

  sortData(column: string, order: 'ascending' | 'descending'): void {
    this.dataViewService
      .SortColumn(
        this.fileID,
        this.currentPageIndex + 1,
        this.pageSize,
        column,
        order,
      )
      .subscribe(
        () => {
          this.loadData();
        },
        error => {
          console.error('Error sorting data:', error);
        },
      );
  }

  addColumn(column: string, position: 'before' | 'after'): void {
    this.selectedAddColumn = column;
    this.columnPosition = position;
    this.openAddColumnModal();
  }

  removeColumn(column: string): void {
    this.dataViewService.DeleteColumn(this.fileID, column).subscribe(
      response => {
        if (response.status === 'success') {
          this.loadData();
        }
      },
      error => {
        console.error('Error fetching column values:', error);
      },
    );
  }
  openFilterDataModal(columnName?: string): void {
    this.isFilterDataModalOpen = true;
    this.loading = true;
    forkJoin([
      this.dataViewService.getColumnChoice(this.fileID), // First observable: getColumnChoice
      this.dataViewService.getColumnValues(this.fileID), // Second observable: getColumnValues
    ]).subscribe({
      next: ([columnChoiceResponse, columnValuesResponse]) => {
        const {
          data: {
            data: { Categories, Numerical },
          },
        } = columnChoiceResponse;

        this.categories = Categories;
        this.numerical = Numerical;

        const { values, operators } = columnValuesResponse.data;

        this.availableValues = values;
        this.availableOperators = operators;

        // If columnName is passed, trigger onColumnChange to select the column
        if (this.filter_obj_id) {
          if (columnName) {
            this.addRow();
          }
          this.filterDataRows.forEach(filterData => {
            const mockEvent = {
              target: { value: filterData?.selectedColumn || columnName },
            } as unknown as Event;
            this.onColumnChange(mockEvent, filterData);
          });
        } else if (columnName) {
          const mockEvent = {
            target: { value: columnName },
          } as unknown as Event;
          this.onColumnChange(mockEvent, this.filterDataRows[0]);
        }
        this.loading = false;
      },
      error: error => {
        this.loading = false;
        console.error('Error fetching column values:', error);
      },
    });
  }

  getColumnValues(): void {
    this.dataViewService.getColumnValues(this.fileID).subscribe(
      response => {
        const { values, operators } = response.data;
        this.availableValues = values;
        this.availableOperators = operators;
      },
      error => {
        console.error('Error fetching column values:', error);
      },
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onColumnChange(event: any, row: FilterDataRow): void {
    const selectedColumn = event.value || event.target.value;
    row.selectedColumn = selectedColumn;

    if (this.numerical.includes(selectedColumn)) {
      row.columnType = 'Numerical';
      const columnData = this.availableValues?.Numerical[selectedColumn];
      // Set placeholders for min and max values
      row.minPlaceholder = columnData?.min.toString();
      row.maxPlaceholder = columnData?.max.toString() || '';
    } else if (this.categories.includes(selectedColumn)) {
      row.columnType = 'Categories';
      const matchingCategory = this.availableValues?.Categories?.find(
        category => category.name === selectedColumn,
      );

      // If a matching category is found, set the available values
      row.availableValues = matchingCategory ? matchingCategory.options : [];
    }

    // Update available operators based on the column type
    row.availableOperators =
      row.columnType &&
      this.availableOperators &&
      this.availableOperators[row.columnType]
        ? Object.keys(this.availableOperators[row.columnType])
        : [];
  }

  onOperatorChange(event: MatSelectChange, row: FilterDataRow) {
    console.log(event);
    console.log(row);
    row.selectedOperator = event.value;
    row.isBetweenOperator = row.selectedOperator === 'between';
  }

  addRow() {
    this.filterDataRows.push({
      logicOperator: '',
      selectedColumn: '',
      selectedOperator: '',
      isBetweenOperator: false,
      selectedValue: '',
    });
  }

  removeRow(index: number) {
    this.filterDataRows.splice(index, 1);
  }
  closeFilterDataModal(): void {
    this.isFilterDataModalOpen = false;
    this.resetFilterDataModal();
  }
  openAddColumnModal(): void {
    this.isAddColumnModalOpen = true;
    this.isAddColumnFormSubmitted = false;

    this.dataViewService.getColumnChoice(this.fileID).subscribe(
      response => {
        this.numerical = response.data.data.Numerical;

        this.getColumnValues();
      },
      error => {
        console.error('Error fetching column values:', error);
      },
    );
  }
  addAnotherColumn() {
    this.dynamicAddColumn.push({
      col1: '',
      operation: '',
      col2_or_scalar: '',
      metric_name: '',
    });
  }
  closeAddColumnModal(): void {
    this.isAddColumnModalOpen = false;
    this.resetAddColumnForm();
  }
  filterDataSave() {
    const filters = this.filterDataRows.map((row, index) => {
      const logicOperator = index === 0 ? 'AND' : row.logicOperator;
      const selectedColumn = row.selectedColumn;
      const operator = row.selectedOperator;

      const value =
        row.columnType === 'Categories'
          ? row.selectedValue
          : row.isBetweenOperator
            ? `${row.minValue}, ${row.maxValue}`
            : row.singleValue;

      return {
        filter_column: selectedColumn,
        filter_operator: operator,
        filter_value: value,
        logic: logicOperator,
      };
    });

    const filterData = JSON.stringify({ filters }, null, 2);

    this.dataViewService
      .FilterColumn(
        this.fileID,
        filterData,
        this.currentPageIndex + 1,
        this.pageSize,
      )
      .subscribe({
        next: response => {
          if (response.status === 'success') {
            this.isFilterDataModalOpen = false;
            this.resetFilterDataModal();

            // Update filter status
            this.filter_active = true;
            this.filter_obj_id = response.data.filter_obj_id;

            // Update table data directly from the response instead of making another API call
            if (response.data.result_data) {
              this.dataSource.data = response.data.result_data;
              this.toastrService.success('Filter applied successfully');
            }
          }
        },
        error: error => {
          console.error('Error sending filters:', error);
        },
      });
  }
  selectOption(option: string) {
    this.filterDataRows[0].selectedValue = option;
    this.showDropdown = false;
  }
  selectValue(option: string, row: FilterDataRow) {
    row.selectedValue = option;
    row.showValues = false;
  }
  hideDropdownWithDelay() {
    setTimeout(() => {
      this.showDropdown = false;
    }, 200); // Delay to allow clicking on options
  }
  removeFilter() {
    if (this.filter_obj_id) {
      this.dataViewService.RemoveFilter(this.filter_obj_id).subscribe(
        response => {
          if (response.status === 'success') {
            this.isFilterDataModalOpen = false;
            this.resetFilterDataModal();
            this.loadData();
            this.toastrService.success('Filter removed successfully');
          }
        },
        error => {
          console.error('Error removing filter:', error);
        },
      );
    }
  }

  isFormValid(): boolean {
    return this.filterDataRows.every((row, index) => {
      if (index === 0) {
        return row.selectedColumn && row.selectedOperator !== '';
      }

      if (!row.logicOperator || !row.selectedColumn || !row.selectedOperator) {
        return false;
      }

      if (row.isBetweenOperator) {
        return row.minValue !== '' && row.maxValue !== '';
      }

      return row.singleValue !== '';
    });
  }

  addModalSave(form: NgForm): void {
    this.isAddColumnFormSubmitted = true;
    if (form.invalid) {
      return;
    }
    this.dataViewService
      .AddColumn(
        this.fileID,
        this.dynamicAddColumn,
        this.selectedAddColumn || '',
        this.columnPosition || '',
      )
      .subscribe({
        next: response => {
          if (response.status === 'success') {
            // Close the modal
            this.isAddColumnModalOpen = false;

            // Reset the form after successful column addition
            this.resetAddColumnForm();

            // Reload the data
            this.loadData();
          }
        },
        error: error => {
          console.error('Error adding column:', error);
          this.toastrService.error(error.error?.message, 'Error adding column');
        },
      });
  }

  resetAddColumnForm() {
    this.dynamicAddColumn = [
      { col1: '', operation: '', col2_or_scalar: '', metric_name: '' },
    ];
    this.selectedAddColumn = '';
    this.columnPosition = '';
    this.newColumnName = '';
    this.selectedColumn1 = '';
    this.selectedOperator = '';
    this.selectedColumn2 = '';
    this.newColumnName = '';
    this.selectedColumn1 = '';
    this.selectedAddColOperator = '';
    this.selectedColumn2 = '';
    this.isAddColumnFormSubmitted = false;
  }

  resetFilterDataModal() {
    this.categories = [];
    this.numerical = [];
    this.addColumn1Numerical = [];
    this.addColumn2Numerical = [];
    this.columnType = '';
    this.selectedOperator = '';
    this.selectedAddColumn = '';
    this.columnPosition = '';
    this.filterDataRows = [
      {
        logicOperator: '',
        selectedColumn: '',
        selectedOperator: '',
        isBetweenOperator: false,
        columnType: '',
        availableOperators: [],
        availableValues: [],
        selectedValue: '',
        minValue: null,
        maxValue: null,
        singleValue: null,
      },
    ];
  }
}
